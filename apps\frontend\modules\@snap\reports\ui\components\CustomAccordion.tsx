import * as React from "react"
import * as AccordionPrimitive from "@radix-ui/react-accordion"
import { ChevronDown } from "lucide-react"

import { cn } from "./utils"
import { 
  Accordion, 
  AccordionItem as BaseAccordionItem, 
  AccordionTrigger as BaseAccordionTrigger, 
  AccordionContent as BaseAccordionContent 
} from "./base/accordion"

// Re-export the root Accordion component
export { Accordion }

// Custom AccordionItem component
export const AccordionItem = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>
>(({ className, ...props }, ref) => (
  <BaseAccordionItem
    ref={ref}
    className={cn("border-b", className)}
    {...props}
  />
))
AccordionItem.displayName = "CustomAccordionItem"

// Custom AccordionTrigger component
export const AccordionTrigger = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <BaseAccordionTrigger
    ref={ref}
    className={cn(
      "flex flex-1 items-center justify-start gap-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",
      className,
    )}
    {...props}
  >
    {children}
  </BaseAccordionTrigger>
))
AccordionTrigger.displayName = "CustomAccordionTrigger"

// Custom AccordionContent component
export const AccordionContent = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <BaseAccordionContent
    ref={ref}
    className={cn("overflow-hidden text-sm transition-all", className)}
    {...props}
  >
    {children}
  </BaseAccordionContent>
))
AccordionContent.displayName = "CustomAccordionContent"