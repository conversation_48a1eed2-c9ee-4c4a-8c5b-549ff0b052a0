import * as React from "react";
import * as AccordionPrimitive from "@radix-ui/react-accordion";
import {
  Accordion as ShadcnAccordion,
  AccordionItem as ShadcnAccordionItem,
  AccordionContent as ShadcnAccordionContent,
} from "./base/accordion";
import { ChevronDown } from "lucide-react";
import { cn } from "./utils";

interface AccordionRootProps {
  children: React.ReactNode;
  type?: "single" | "multiple";
  collapsible?: boolean;
  defaultValue?: string | string[];
  value?: string | string[];
  onValueChange?: (value: string | string[]) => void;
  className?: string;
}

interface AccordionItemProps {
  children: React.ReactNode;
  value: string;
  className?: string;
  disabled?: boolean;
}

interface AccordionTriggerProps {
  children: React.ReactNode;
  className?: string;
}

interface AccordionContentProps {
  children: React.ReactNode;
  className?: string;
}

function AccordionRoot({ children, type = "multiple", ...props }: AccordionRootProps) {
  return (
    <ShadcnAccordion type={type} {...props as any}>
      {children}
    </ShadcnAccordion>
  );
}

function AccordionItem({ children, className = "", ...props }: AccordionItemProps) {
  return (
    <ShadcnAccordionItem
      className={cn("border-b-0", className)}
      {...props}
    >
      {children}
    </ShadcnAccordionItem>
  );
}

const AccordionTrigger = React.forwardRef<
  React.ComponentRef<typeof AccordionPrimitive.Trigger>,
  AccordionTriggerProps
>(({ children, className = "", ...props }, ref) => (
  <AccordionPrimitive.Header className="flex">
    <AccordionPrimitive.Trigger
      ref={ref}
      className={cn(
        "flex flex-1 items-center justify-start gap-4 font-medium transition-all hover:underline [&[data-state=open]>div>svg]:rotate-180 bg-accordion-header px-0 py-0 mb-4 data-[state=open]:mb-0 no-underline [&_div:first-child]:border-neutral-100 cursor-pointer",
        className
      )}
      {...props}
    >
      <div className="border-r border-dashed p-3">
        <ChevronDown className="h-4 w-4 shrink-0 transition-transform duration-200" />
      </div>
      {children}
    </AccordionPrimitive.Trigger>
  </AccordionPrimitive.Header>
))
AccordionTrigger.displayName = "CustomAccordionTrigger"

function AccordionContent({ children, className = "", ...props }: AccordionContentProps) {
  return (
    <ShadcnAccordionContent
      className={cn("bg-card px-5", className)}
      {...props}
    >
      <div className="pt-5">
        {children}
      </div>
    </ShadcnAccordionContent>
  );
}

export const Accordion = Object.assign(AccordionRoot, {
  Item: AccordionItem,
  Trigger: AccordionTrigger,
  Content: AccordionContent,
});