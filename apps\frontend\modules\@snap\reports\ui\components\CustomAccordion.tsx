import * as React from "react";
import {
  Accordion as ShadcnAccordion,
  AccordionItem as ShadcnAccordionItem,
  AccordionTrigger as <PERSON>hadcnAccordionTrigger,
  AccordionContent as ShadcnAccordionContent,
} from "./base/accordion";
import { cn } from "./utils";

interface AccordionRootProps {
  children: React.ReactNode;
  type?: "single" | "multiple";
  collapsible?: boolean;
  defaultValue?: string | string[];
  value?: string | string[];
  onValueChange?: (value: string | string[]) => void;
  className?: string;
}

interface AccordionItemProps {
  children: React.ReactNode;
  value: string;
  className?: string;
  disabled?: boolean;
}

interface AccordionTriggerProps {
  children: React.ReactNode;
  className?: string;
}

interface AccordionContentProps {
  children: React.ReactNode;
  className?: string;
}

function AccordionRoot({ children, type = "multiple", ...props }: AccordionRootProps) {
  return (
    <ShadcnAccordion type={type} {...props as any}>
      {children}
    </ShadcnAccordion>
  );
}

function AccordionItem({ children, className = "", ...props }: AccordionItemProps) {
  return (
    <ShadcnAccordionItem
      className={cn("border-b-0", className)}
      {...props}
    >
      {children}
    </ShadcnAccordionItem>
  );
}

function AccordionTrigger({ children, className = "", ...props }: AccordionTriggerProps) {
  return (
    <ShadcnAccordionTrigger
      className={cn(
        "bg-accordion-header px-0 py-0 mb-4 data-[state=open]:mb-0 no-underline [&_div:first-child]:border-neutral-100 cursor-pointer",
        className
      )}
      {...props}
    >
      {children}
    </ShadcnAccordionTrigger>
  );
}

function AccordionContent({ children, className = "", ...props }: AccordionContentProps) {
  return (
    <ShadcnAccordionContent
      className={cn("bg-card px-5", className)}
      {...props}
    >
      <div className="pt-5">
        {children}
      </div>
    </ShadcnAccordionContent>
  );
}

export const Accordion = Object.assign(AccordionRoot, {
  Item: AccordionItem,
  Trigger: AccordionTrigger,
  Content: AccordionContent,
});