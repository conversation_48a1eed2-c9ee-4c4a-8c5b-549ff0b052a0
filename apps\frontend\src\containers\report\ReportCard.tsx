import type { ReportData } from "~/types/global";
import { getInitials, getTypeIcon } from "~/helpers";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ed<PERSON> } from "react-icons/lu";
import {
  SubjectCard,
  Separator,
  Avatar,
  Button,
  StandardList,
  ListItem,
  Text,
} from "@snap/design-system";
import { useNavigate } from "react-router";
import { MdOutlineDriveFileMove } from "react-icons/md";
import { ArrowRight, Download, Pencil, Trash, X } from "lucide-react";
import { BiMerge } from "react-icons/bi";
import { isErrorReport, isPendingReport } from "~/helpers/reportStatus.helper";
import { ReportModel } from "root/domain/entities/report.model";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { useEncryption } from "~/hooks/useEncryption";
import { toast } from "sonner";
import { REPORT_CONSTANTS } from "~/helpers/constants";
import { FaUsers } from "react-icons/fa6";
import { FileStack } from "lucide-react";
import { TbBuildings } from "react-icons/tb";

export default function ReportCard({
  report,
  onAccess,
  onRetry,
}: {
  report: ReportData;
  onAccess?: () => void | undefined;
  onRetry?: () => void | undefined;
}) {
  const { newReportMutation } = useReportCRUD();
  const { encryptData } = useEncryption();
  const normalized = new ReportModel(report);
  const navigate = useNavigate();

  const handleRetryReport = async () => {
    const searchArgs = normalized.searchArgs as Record<string, string[]>;
    const firstKey = Object.keys(searchArgs)[0];
    const valueString = searchArgs[firstKey]?.[0];

    try {
      const encryptedValue = await encryptData(normalized.searchArgs);

      if (!encryptedValue.data) {
        console.error("Error encrypting searchArgs:", encryptedValue.error);
        toast("Erro", {
          description: "Erro ao tentar refazer o relatório",
          action: {
            label: "Fechar",
            onClick: () => { },
          },
        });
        return;
      }

      newReportMutation.mutateAsync({
        report_type: normalized.type,
        report_input_value: valueString,
        report_input_encrypted: encryptedValue.data,
        user_reports_id: normalized.id,
      });
    } catch (error) {
      console.error("Error processing newReportMutation:", error);
      toast("Erro", {
        description: "Erro ao tentar refazer o relatório",
        action: {
          label: "Fechar",
          onClick: () => { },
        },
      });
    }
  };

  const pendingHeader = (
    <div className="flex items-center bg-gray-700 justify-between gap-3 p-3 w-full">
      {getTypeIcon(normalized.type, 32)}
      <Text variant="" className="uppercase">
        {/* @ts-ignore */} {/* TODO - melhorar esse acesso quando tiver mesclado */}
        {`${normalized.type}: ${normalized.searchArgs[normalized.type]?.[0]}`}
      </Text>
    </div>
  );

  if (isPendingReport(report)) {
    return (
      <SubjectCard
        headerClassName="p-0"
        header={
          pendingHeader
        }
        content={
          <div className="relative flex flex-col justify-center items-center p-4">
            <LuClock className="w-12 h-12 text-brand-primary animate-spin" />
            <Text variant="body-md" align="center" className="mt-2">Gerando seu relatório</Text>
          </div>
        }
        footer={null}
        menuWrapperClassName="top-0 right-0"
        menuClassName="border border-card"
        cardClassName="max-w-[282px]"
      />
    );
  }

  if (isErrorReport(report)) {
    return (
      <SubjectCard
        headerClassName="p-0"
        header={
          pendingHeader
        }
        content={
          <div className="relative flex flex-col justify-center items-center p-4">
            <X className="w-12 h-12 text-primary" />
            <Text variant="body-md" align="center" className="mt-2">Ocorreu um erro ao gerar o relatório</Text>
          </div>
        }
        footer={null}
        actions={
          <Button onClick={onAccess ?? handleRetryReport}>Refazer<LuRedo /> </Button>
        }
        menuWrapperClassName="top-0 right-0"
        menuClassName="border border-card"
        cardClassName="max-w-[282px]"
      />
    );
  }

  const initials = getInitials(normalized.subjectName);
  const defaultIcon = <div className="size-3 bg-border" />;
  const redirectTo = `/report/${normalized.type}/${normalized.id}`;

  const listData = [...normalized.searchItems];

  if (normalized.subjectMotherName) {
    listData.unshift({
      label: "MÃE",
      value: normalized.subjectMotherName,
      icon: defaultIcon,
    });
  }

  if (normalized.subjectAge != null && normalized.subjectAge !== "") {
    listData.push({
      label: "IDADE",
      value: normalized.subjectAge,
      icon: defaultIcon,
    });
  }

  if (normalized.subjectSex) {
    listData.push({
      label: "SEXO",
      value: normalized.subjectSex,
      icon: defaultIcon,
    });
  }

  if (normalized.subjectPersonCount) {
    listData.push({
      label: "PESSOAS ENCONTRADAS",
      value: normalized.subjectPersonCount,
      icon: defaultIcon,
    });
  }

  if (normalized.subjectCompanyCount) {
    listData.push({
      label: "EMPRESAS ENCONTRADAS",
      value: normalized.subjectCompanyCount,
      icon: defaultIcon,
    });
  }

  const isMultipleSubjects = normalized.subjectName === REPORT_CONSTANTS.multiplos_registros_encontrados;
  const multipleIcon = normalized.subjectPersonCount ? <FaUsers className="size-5" /> : <TbBuildings className="size-5" />;

  const header = (
    <>
      <div className="flex items-center bg-neutral-700 justify-between gap-3 p-3 w-full">
        {getTypeIcon(normalized.type, 32)}
        <span className="uppercase line-clamp-2 text-ellipsis max-w-4/5">
          {normalized.name}
        </span>
      </div>
      <Separator />
      <div className="p-3">
        {
          !isMultipleSubjects ? (
            <Avatar
              name={normalized.subjectName}
              fallback={initials || "N/A"}
              size="md"
              textAlign="right"
              textClassName="text-lg uppercase"
            />
          ) : (
            <div className="flex items-center justify-between gap-2">
              <div className="border-border border-4 rounded-full p-3">
                {multipleIcon}
              </div>
              <Text variant="body-lg" className="uppercase text-right">
                {normalized.subjectName}
              </Text>
            </div>
          )
        }
      </div>
    </>
  );
  const content = (
    <StandardList withSeparator>
      {listData.map(({ label, value, icon, highlight }) => (
        <ListItem
          key={`${label}-${value}`}
          icon={icon}
          label={label}
          value={value}
          highlight={highlight}
          className="text-foreground"
        />
      ))}
    </StandardList>
  );
  const actions = (
    <Button
      size="sm"
      iconPosition="right"
      icon={<ArrowRight className="size-4" />}
      className="w-full"
      onClick={() => navigate(redirectTo)/* alert("TELA DE DETALHES DO RELATÓRIO ESTÁ EM CONSTRUÇÃO") */} // TODO - voltar função ou usar onAccess quando página de detalhes estiver pronta onClick={() => navigate(redirectTo)}
    >
      Acessar
    </Button>
  );
  const menu = [
    {
      label: "mover para pasta",
      icon: <MdOutlineDriveFileMove className="size-4" />,
      onClick: () => alert("AÇÃO DE MOVER RELATÓRIO ESTÁ EM CONSTRUÇÃO"),
    },
    {
      label: "mesclar relatório",
      icon: <BiMerge className="size-4" />,
      onClick: () => alert("AÇÃO DE MESCLAR RELATÓRIO ESTÁ EM CONSTRUÇÃO"),
    },
    {
      label: "renomear relatório",
      icon: <Pencil className="size-4" />,
      onClick: () => alert("AÇÃO DE RENOMEAR RELATÓRIO ESTÁ EM CONSTRUÇÃO"),
    },
    {
      label: "exportar pdf",
      icon: <Download className="size-4" />,
      onClick: () => alert("AÇÃO DE EXPORTAR PDF ESTÁ EM CONSTRUÇÃO"),
    },
    {
      label: "mover para a lixeira",
      icon: <Trash className="size-4" />,
      onClick: () => alert("AÇÃO DE MOVER PARA A LIXEIRA ESTÁ EM CONSTRUÇÃO"),
    },
  ];

  return (
    <SubjectCard
      headerClassName="p-0"
      header={header}
      content={content}
      actions={actions}
      menu={menu}
      menuWrapperClassName="top-0 right-0"
      menuClassName="border border-card"
      cardClassName="max-w-[282px]"
      border={false}
      footer={{
        createdAt: normalized.requestDate,
        updatedAt: normalized.lastModified,
      }}
    />
  );
}
